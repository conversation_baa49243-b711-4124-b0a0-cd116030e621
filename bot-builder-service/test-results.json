{"passed": 8, "failed": 0, "tests": [{"name": "Health Endpoint", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:28:30.070Z"}, {"name": "GET /api/v1/bots (List)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:28:30.092Z"}, {"name": "GET /api/v1/bots/{id} (Single)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:28:30.101Z"}, {"name": "Bot 404 Error <PERSON>ling", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:28:30.103Z"}, {"name": "GET /api/v1/entities (List)", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:28:30.108Z"}, {"name": "Swagger JSON Endpoint", "status": "PASS", "details": "25 endpoints documented", "timestamp": "2025-07-19T17:28:30.111Z"}, {"name": "Swagger UI Endpoint", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:28:30.115Z"}, {"name": "ApiResponse Schema Compliance", "status": "PASS", "details": "", "timestamp": "2025-07-19T17:28:30.118Z"}]}
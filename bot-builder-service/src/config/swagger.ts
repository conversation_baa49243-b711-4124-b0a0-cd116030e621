/**
 * Swagger Configuration for Bot Builder Service
 *
 * OpenAPI 3.0 specification for all administrative APIs
 */

import swaggerJsdoc from "swagger-jsdoc";
import { SwaggerDefinition } from "swagger-jsdoc";

const swaggerDefinition: SwaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Bot Builder Service API",
    version: "1.0.0",
    description:
      "Administrative API for managing bots, flows, and configurations in the no-code chatbot platform",
    contact: {
      name: "Chatbot Platform Team",
      email: "<EMAIL>",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  servers: [
    {
      url: "http://localhost:3000",
      description: "Development server",
    },
    {
      url: "https://api.chatbot-platform.com",
      description: "Production server",
    },
  ],
  components: {
    securitySchemes: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "X-API-Key",
        description: "API key for authentication",
      },
      BearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "JWT token for user authentication",
      },
    },
    schemas: {
      Bot: {
        type: "object",
        required: ["id", "name", "status"],
        properties: {
          id: {
            type: "string",
            format: "uuid",
            description: "Unique bot identifier",
            example: "123e4567-e89b-12d3-a456-************",
          },
          name: {
            type: "string",
            description: "Bot name",
            example: "Customer Support Bot",
            minLength: 1,
            maxLength: 255,
          },
          description: {
            type: "string",
            description: "Bot description",
            example: "Handles customer inquiries and support requests",
            maxLength: 1000,
          },
          status: {
            type: "string",
            enum: ["active", "inactive", "draft"],
            description: "Bot status",
            example: "active",
          },
          settings: {
            type: "object",
            description: "Bot configuration settings",
            properties: {
              nlu: {
                type: "object",
                properties: {
                  provider: { type: "string", example: "rasa" },
                  confidenceThreshold: { type: "number", example: 0.7 },
                },
              },
              session: {
                type: "object",
                properties: {
                  ttlMinutes: { type: "number", example: 30 },
                  maxConcurrentSessions: { type: "number", example: 10 },
                },
              },
            },
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
          createdAt: {
            type: "string",
            format: "date-time",
            description: "Creation timestamp",
          },
          updatedAt: {
            type: "string",
            format: "date-time",
            description: "Last update timestamp",
          },
          createdBy: {
            type: "string",
            description: "User who created the bot",
          },
          updatedBy: {
            type: "string",
            description: "User who last updated the bot",
          },
        },
      },
      Flow: {
        type: "object",
        required: ["id", "name", "botId"],
        properties: {
          id: {
            type: "string",
            format: "uuid",
            description: "Unique flow/journey identifier",
            example: "123e4567-e89b-12d3-a456-************",
          },
          name: {
            type: "string",
            description: "Flow/journey name",
            example: "Customer Onboarding Journey",
          },
          description: {
            type: "string",
            description: "Flow/journey description",
            example: "Journey for new customer registration",
          },
          botId: {
            type: "string",
            format: "uuid",
            description: "Associated bot ID",
            example: "123e4567-e89b-12d3-a456-************",
          },
          type: {
            type: "string",
            description: "Type of flow (journey, conversation, etc.)",
            example: "journey",
            enum: ["journey", "conversation", "flow"],
          },
          appId: {
            type: "string",
            format: "uuid",
            description: "Associated app ID",
            example: "123e4567-e89b-12d3-a456-************",
          },
          version: {
            type: "integer",
            description: "Flow/journey version number",
            example: 1,
          },
          isActive: {
            type: "boolean",
            description: "Whether the flow/journey is active",
            example: true,
          },
          entryNodeId: {
            type: "string",
            description: "ID of the entry node",
            example: "intent_greet",
          },
          nodes: {
            type: "object",
            description: "Flow/journey nodes definition",
            additionalProperties: {
              $ref: "#/components/schemas/Node",
            },
          },
          metadata: {
            type: "object",
            description: "Additional metadata for the flow/journey",
            example: {
              journeyId: "journey-123",
              version: 1,
            },
            additionalProperties: true,
          },
          createdAt: {
            type: "string",
            format: "date-time",
            example: "2023-01-01T12:00:00Z",
          },
          updatedAt: {
            type: "string",
            format: "date-time",
            example: "2023-01-02T12:00:00Z",
          },
          createdBy: {
            type: "string",
            description: "User who created the flow/journey",
            example: "user-123",
          },
          updatedBy: {
            type: "string",
            description: "User who last updated the flow/journey",
            example: "user-123",
          },
        },
      },
      Node: {
        type: "object",
        required: ["id", "type", "condition"],
        properties: {
          id: {
            type: "string",
            description: "Node identifier",
          },
          type: {
            type: "string",
            enum: ["intent", "message", "form", "request", "script", "flow_connector"],
            description: "Node type",
          },
          condition: {
            type: "array",
            items: {
              $ref: "#/components/schemas/Condition",
            },
            description: "Conditions for routing to next node",
          },
        },
        discriminator: {
          propertyName: "type",
        },
      },
      Condition: {
        type: "object",
        required: ["than"],
        properties: {
          variable: {
            type: "string",
            description: "Context variable path",
            example: "context.user.age",
          },
          value: {
            description: "Expected value for comparison",
            example: 18,
          },
          type: {
            type: "string",
            enum: [
              "equal",
              "not_equal",
              "is_defined",
              "is_undefined",
              "greater_than",
              "less_than",
              "contains",
              "regex_match",
            ],
            description: "Comparison type",
            example: "greater_than",
          },
          than: {
            type: "string",
            description: "Next node ID",
            example: "message_welcome",
          },
        },
      },
      CreateBotRequest: {
        type: "object",
        required: ["name"],
        properties: {
          name: {
            type: "string",
            description: "Bot name",
            example: "Customer Support Bot",
          },
          description: {
            type: "string",
            description: "Bot description",
          },
          settings: {
            type: "object",
            description: "Bot settings",
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
          },
        },
      },
      UpdateBotRequest: {
        type: "object",
        properties: {
          name: {
            type: "string",
            description: "Bot name",
          },
          description: {
            type: "string",
            description: "Bot description",
          },
          settings: {
            type: "object",
            description: "Bot settings",
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
          },
        },
      },
      CreateFlowRequest: {
        type: "object",
        required: ["name", "botId", "entryNodeId", "nodes"],
        properties: {
          name: {
            type: "string",
            description: "Flow name",
          },
          description: {
            type: "string",
            description: "Flow description",
          },
          botId: {
            type: "string",
            format: "uuid",
            description: "Associated bot ID",
          },
          entryNodeId: {
            type: "string",
            description: "Entry node ID",
          },
          nodes: {
            type: "object",
            description: "Flow nodes",
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
          },
        },
      },
      ApiResponse: {
        type: "object",
        required: ["success", "timestamp"],
        properties: {
          success: {
            type: "boolean",
            description: "Whether the request was successful",
          },
          data: {
            description: "Response data (present on success)",
          },
          error: {
            type: "object",
            description: "Error details (present on failure)",
            properties: {
              code: {
                type: "string",
                description: "Error code",
              },
              message: {
                type: "string",
                description: "Error message",
              },
              details: {
                description: "Additional error details",
              },
            },
          },
          timestamp: {
            type: "string",
            format: "date-time",
            description: "Response timestamp",
          },
        },
      },
      PaginationResponse: {
        type: "object",
        properties: {
          items: {
            type: "array",
            description: "Array of items",
          },
          pagination: {
            type: "object",
            properties: {
              page: { type: "integer", example: 1 },
              limit: { type: "integer", example: 20 },
              total: { type: "integer", example: 100 },
              totalPages: { type: "integer", example: 5 },
              hasNext: { type: "boolean", example: true },
              hasPrev: { type: "boolean", example: false },
            },
          },
        },
      },
      Pagination: {
        type: "object",
        properties: {
          page: { type: "integer", example: 1 },
          limit: { type: "integer", example: 20 },
          total: { type: "integer", example: 100 },
          totalPages: { type: "integer", example: 5 },
          hasNext: { type: "boolean", example: true },
          hasPrev: { type: "boolean", example: false },
        },
      },
      FaqItems: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          questions: { type: "array", items: { type: "string" } },
          answer: { type: "string" },
          botId: { type: "string", format: "uuid" },
          flowId: { type: "string", format: "uuid", nullable: true },
          category: { type: "string" },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      IntentItems: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          botId: { type: "string", format: "uuid" },
          flowId: { type: "string", format: "uuid", nullable: true },
          examples: { type: "array", items: { type: "object" } },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      Entities: {
        type: "object",
        properties: {
          id: { type: "string", format: "uuid" },
          name: { type: "string" },
          botId: { type: "string", format: "uuid" },
          intentId: { type: "string", format: "uuid", nullable: true },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      // FAQ Category Schemas
      FaqCategory: {
        type: "object",
        required: ["id", "name", "botId"],
        properties: {
          id: { type: "string", format: "uuid", description: "Unique FAQ category identifier" },
          name: { type: "string", description: "Category name", example: "General Questions" },
          description: {
            type: "string",
            description: "Category description",
            example: "General questions about our service",
          },
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      CreateFaqCategoryRequest: {
        type: "object",
        required: ["name", "botId"],
        properties: {
          name: { type: "string", description: "Category name", example: "General Questions" },
          description: {
            type: "string",
            description: "Category description",
            example: "General questions about our service",
          },
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
        },
      },
      UpdateFaqCategoryRequest: {
        type: "object",
        properties: {
          name: { type: "string", description: "Category name" },
          description: { type: "string", description: "Category description" },
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
        },
      },
      // FAQ Translation Schemas
      FaqTranslation: {
        type: "object",
        required: ["id", "faqId", "langId", "questions", "answer"],
        properties: {
          id: { type: "string", format: "uuid", description: "Unique FAQ translation identifier" },
          faqId: { type: "string", format: "uuid", description: "Associated FAQ item ID" },
          langId: { type: "string", format: "uuid", description: "Language ID" },
          questions: {
            type: "array",
            items: { type: "string" },
            description: "List of questions",
            example: ["How do I reset my password?", "Password reset help"],
          },
          answer: {
            type: "string",
            description: "Answer text",
            example: "To reset your password, click on the 'Forgot Password' link...",
          },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      CreateFaqTranslationRequest: {
        type: "object",
        required: ["faqId", "langId", "questions", "answer"],
        properties: {
          faqId: { type: "string", format: "uuid", description: "Associated FAQ item ID" },
          langId: { type: "string", format: "uuid", description: "Language ID" },
          questions: {
            type: "array",
            items: { type: "string" },
            minItems: 1,
            description: "List of questions",
          },
          answer: { type: "string", description: "Answer text" },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
        },
      },
      UpdateFaqTranslationRequest: {
        type: "object",
        properties: {
          faqId: { type: "string", format: "uuid", description: "Associated FAQ item ID" },
          langId: { type: "string", format: "uuid", description: "Language ID" },
          questions: {
            type: "array",
            items: { type: "string" },
            minItems: 1,
            description: "List of questions",
          },
          answer: { type: "string", description: "Answer text" },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
        },
      },
      // Intent Utterance Schemas
      IntentUtterance: {
        type: "object",
        required: ["id", "intentId"],
        properties: {
          id: { type: "string", format: "uuid", description: "Unique intent utterance identifier" },
          intentId: { type: "string", format: "uuid", description: "Associated intent ID" },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      CreateIntentUtteranceRequest: {
        type: "object",
        required: ["intentId"],
        properties: {
          intentId: { type: "string", format: "uuid", description: "Associated intent ID" },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
        },
      },
      UpdateIntentUtteranceRequest: {
        type: "object",
        properties: {
          intentId: { type: "string", format: "uuid", description: "Associated intent ID" },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
        },
      },
      // Intent Utterance Translation Schemas
      IntentUtteranceTranslation: {
        type: "object",
        required: ["id", "utteranceId", "langId", "text"],
        properties: {
          id: { type: "string", format: "uuid", description: "Unique translation identifier" },
          utteranceId: { type: "string", format: "uuid", description: "Associated utterance ID" },
          langId: { type: "string", format: "uuid", description: "Language ID" },
          text: {
            type: "string",
            description: "Utterance text",
            example: "I want to book a flight",
          },
          entities: {
            type: "object",
            description: "Extracted entities",
            additionalProperties: true,
          },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      CreateIntentUtteranceTranslationRequest: {
        type: "object",
        required: ["utteranceId", "langId", "text"],
        properties: {
          utteranceId: { type: "string", format: "uuid", description: "Associated utterance ID" },
          langId: { type: "string", format: "uuid", description: "Language ID" },
          text: { type: "string", description: "Utterance text" },
          entities: {
            type: "object",
            description: "Extracted entities",
            additionalProperties: true,
          },
        },
      },
      UpdateIntentUtteranceTranslationRequest: {
        type: "object",
        properties: {
          utteranceId: { type: "string", format: "uuid", description: "Associated utterance ID" },
          langId: { type: "string", format: "uuid", description: "Language ID" },
          text: { type: "string", description: "Utterance text" },
          entities: {
            type: "object",
            description: "Extracted entities",
            additionalProperties: true,
          },
        },
      },
      // Language Schemas
      Language: {
        type: "object",
        required: ["id", "name", "code"],
        properties: {
          id: { type: "string", format: "uuid", description: "Unique language identifier" },
          name: { type: "string", description: "Language name", example: "English" },
          code: { type: "string", description: "Language code", example: "en" },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      CreateLanguageRequest: {
        type: "object",
        required: ["name", "code"],
        properties: {
          name: { type: "string", description: "Language name", example: "English" },
          code: { type: "string", description: "Language code", example: "en" },
        },
      },
      UpdateLanguageRequest: {
        type: "object",
        properties: {
          name: { type: "string", description: "Language name" },
          code: { type: "string", description: "Language code" },
        },
      },
      // Bot Language Schemas
      BotLanguage: {
        type: "object",
        required: ["id", "botId", "langId"],
        properties: {
          id: { type: "string", format: "uuid", description: "Unique bot language identifier" },
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
          langId: { type: "string", format: "uuid", description: "Associated language ID" },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
          deletedAt: { type: "string", format: "date-time", nullable: true },
          createdBy: { type: "string", format: "uuid" },
          updatedBy: { type: "string", format: "uuid" },
          deletedBy: { type: "string", format: "uuid", nullable: true },
        },
      },
      CreateBotLanguageRequest: {
        type: "object",
        required: ["botId", "langId"],
        properties: {
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
          langId: { type: "string", format: "uuid", description: "Associated language ID" },
        },
      },
      UpdateBotLanguageRequest: {
        type: "object",
        properties: {
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
          langId: { type: "string", format: "uuid", description: "Associated language ID" },
        },
      },
      // Entity Request Schemas
      CreateEntityRequest: {
        type: "object",
        required: ["name", "botId", "intentId"],
        properties: {
          name: { type: "string", description: "Entity name", example: "location" },
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
          intentId: { type: "string", format: "uuid", description: "Associated intent ID" },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
        },
      },
      UpdateEntityRequest: {
        type: "object",
        properties: {
          name: { type: "string", description: "Entity name" },
          botId: { type: "string", format: "uuid", description: "Associated bot ID" },
          intentId: { type: "string", format: "uuid", description: "Associated intent ID" },
          metadata: {
            type: "object",
            description: "Additional metadata",
            additionalProperties: true,
          },
        },
      },
      // App Schemas
      App: {
        type: "object",
        required: ["id", "name"],
        properties: {
          id: { type: "string", format: "uuid", description: "Unique app identifier" },
          name: { type: "string", description: "App name", example: "My Chatbot App" },
          desc: {
            type: "string",
            description: "App description",
            example: "A customer service chatbot",
          },
          appData: {
            type: "object",
            description: "App configuration data",
            additionalProperties: true,
          },
          alignment: { type: "string", description: "App alignment" },
          svg: { type: "string", description: "App SVG icon" },
          OTC: { type: "string", description: "One-time charge" },
          MRC: { type: "string", description: "Monthly recurring charge" },
          createdAt: { type: "string", format: "date-time" },
          updatedAt: { type: "string", format: "date-time" },
        },
      },
      CreateAppRequest: {
        type: "object",
        required: ["name"],
        properties: {
          name: { type: "string", description: "App name", maxLength: 32 },
          desc: { type: "string", description: "App description", maxLength: 128 },
          appData: {
            type: "object",
            description: "App configuration data",
            additionalProperties: true,
          },
          alignment: { type: "string", description: "App alignment" },
          svg: { type: "string", description: "App SVG icon" },
          OTC: { type: "string", description: "One-time charge" },
          MRC: { type: "string", description: "Monthly recurring charge" },
        },
      },
      UpdateAppRequest: {
        type: "object",
        properties: {
          name: { type: "string", description: "App name", maxLength: 32 },
          desc: { type: "string", description: "App description", maxLength: 128 },
          appData: {
            type: "object",
            description: "App configuration data",
            additionalProperties: true,
          },
          alignment: { type: "string", description: "App alignment" },
          svg: { type: "string", description: "App SVG icon" },
          OTC: { type: "string", description: "One-time charge" },
          MRC: { type: "string", description: "Monthly recurring charge" },
        },
      },
      // Additional Response Schemas
      FaqCategoryResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/FaqCategory" },
            },
          },
        ],
      },
      FaqCategoriesResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/FaqCategory" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      FaqTranslationResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/FaqTranslation" },
            },
          },
        ],
      },
      FaqTranslationsResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/FaqTranslation" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      IntentUtteranceResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/IntentUtterance" },
            },
          },
        ],
      },
      IntentUtterancesResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/IntentUtterance" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      IntentUtteranceTranslationResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/IntentUtteranceTranslation" },
            },
          },
        ],
      },
      IntentUtteranceTranslationsResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/IntentUtteranceTranslation" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      LanguageResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/Language" },
            },
          },
        ],
      },
      LanguagesResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/Language" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      BotLanguageResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/BotLanguage" },
            },
          },
        ],
      },
      BotLanguagesResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/BotLanguage" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      EntityResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/Entities" },
            },
          },
        ],
      },
      EntitiesResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/Entities" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      AppResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: { $ref: "#/components/schemas/App" },
            },
          },
        ],
      },
      AppsResponse: {
        allOf: [
          { $ref: "#/components/schemas/ApiResponse" },
          {
            type: "object",
            properties: {
              data: {
                allOf: [
                  { $ref: "#/components/schemas/PaginationResponse" },
                  {
                    type: "object",
                    properties: {
                      items: {
                        type: "array",
                        items: { $ref: "#/components/schemas/App" },
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
    },
  },
  tags: [
    {
      name: "Health",
      description: "Health check and system status endpoints",
    },
    {
      name: "Apps",
      description: "Application management operations",
    },
    {
      name: "Bots",
      description: "Bot management operations - Create, configure, and manage chatbots",
    },
    {
      name: "Flows",
      description: "Flow management operations - Design and manage conversation flows",
    },
    {
      name: "FAQ Categories",
      description: "FAQ category management operations",
    },
    {
      name: "FAQ Items",
      description: "FAQ item management operations",
    },
    {
      name: "FAQ Translations",
      description: "FAQ translation management operations",
    },
    {
      name: "Intent Items",
      description: "Intent item management operations",
    },
    {
      name: "Intent Utterances",
      description: "Intent utterance management operations",
    },
    {
      name: "Intent Utterance Translations",
      description: "Intent utterance translation management operations",
    },
    {
      name: "Entities",
      description: "Entity management operations",
    },
    {
      name: "Languages",
      description: "Language management operations",
    },
    {
      name: "Bot Languages",
      description: "Bot language configuration operations",
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    "./src/controllers/*.ts",
    "./src/routes/*.ts",
    "../routes/*.ts",
    "../controllers/*.ts",
    "./routes/*.ts",
    "./controllers/*.ts",
  ],
  swaggerOptions: {
    persistAuthorization: true,
  },
};

export const swaggerSpec = swaggerJsdoc(options);
export default swaggerSpec;

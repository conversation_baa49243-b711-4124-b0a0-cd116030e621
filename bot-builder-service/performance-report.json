{"endpoints": [{"name": "Health Check", "url": "http://localhost:3000/health", "method": "GET", "status": 200, "responseTime": 17, "success": true, "timestamp": "2025-07-19T17:20:49.482Z", "dataSize": 172}, {"name": "Swagger JSON", "url": "http://localhost:3000/api-docs.json", "method": "GET", "status": 200, "responseTime": 9, "success": true, "timestamp": "2025-07-19T17:20:49.495Z", "dataSize": 67584}, {"name": "<PERSON><PERSON> (Small)", "url": "http://localhost:3000/api/v1/bots?limit=5", "method": "GET", "status": 200, "responseTime": 17, "success": true, "timestamp": "2025-07-19T17:20:49.513Z", "dataSize": 1560}, {"name": "Bo<PERSON> (Large)", "url": "http://localhost:3000/api/v1/bots?limit=50", "method": "GET", "status": 200, "responseTime": 3, "success": true, "timestamp": "2025-07-19T17:20:49.516Z", "dataSize": 1561}, {"name": "Entity List", "url": "http://localhost:3000/api/v1/entities?limit=10", "method": "GET", "status": 200, "responseTime": 2, "success": true, "timestamp": "2025-07-19T17:20:49.518Z", "dataSize": 167}, {"name": "FAQ Categories", "url": "http://localhost:3000/api/v1/faq-categories?limit=10", "method": "GET", "status": 200, "responseTime": 5, "success": true, "timestamp": "2025-07-19T17:20:49.523Z", "dataSize": 167}, {"name": "FAQ Items", "url": "http://localhost:3000/api/v1/faq-items?limit=10", "method": "GET", "status": 200, "responseTime": 6, "success": true, "timestamp": "2025-07-19T17:20:49.529Z", "dataSize": 167}, {"name": "Intent Items", "url": "http://localhost:3000/api/v1/intent-items?limit=10", "method": "GET", "status": 200, "responseTime": 4, "success": true, "timestamp": "2025-07-19T17:20:49.533Z", "dataSize": 167}, {"name": "Languages", "url": "http://localhost:3000/api/v1/languages?limit=10", "method": "GET", "status": 200, "responseTime": 6, "success": true, "timestamp": "2025-07-19T17:20:49.539Z", "dataSize": 1323}, {"name": "Apps", "url": "http://localhost:3000/api/v1/apps?limit=10", "method": "GET", "status": 200, "responseTime": 32, "success": true, "timestamp": "2025-07-19T17:20:49.571Z", "dataSize": 63070}, {"name": "404 Error Test", "url": "http://localhost:3000/api/v1/bots/00000000-0000-0000-0000-000000000000", "method": "GET", "status": 404, "responseTime": 1, "success": false, "timestamp": "2025-07-19T17:20:49.573Z", "dataSize": 115}], "summary": {"totalTests": 11, "averageResponseTime": 10.1, "fastestEndpoint": {"name": "Entity List", "responseTime": 2}, "slowestEndpoint": {"name": "Apps", "responseTime": 32}, "errorRate": 9.090909090909092}}
# Swagger Documentation Template

This template provides a standardized format for documenting API endpoints in the bot-builder-service. Use this template when adding new endpoints or modifying existing ones to maintain consistency across the API documentation.

## Basic Endpoint Template

```yaml
/**
 * @swagger
 * /api/v1/{resource}:
 *   {method}:
 *     summary: Brief description of the endpoint
 *     description: Detailed description of what the endpoint does
 *     tags: [{ResourceName}]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Resource unique identifier
 *         example: "123e4567-e89b-12d3-a456-************"
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter results
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Create{ResourceName}Request'
 *           example:
 *             name: "Example Resource"
 *             description: "Example description"
 *     responses:
 *       200:
 *         description: Operation successful
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/{ResourceName}'
 *       201:
 *         description: Resource created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/{ResourceName}'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       404:
 *         description: Resource not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NotFoundErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/InternalServerErrorResponse'
 */
```

## HTTP Method Templates

### GET (List Resources)
- Use `PaginatedResults` schema for list endpoints
- Include pagination parameters (page, limit)
- Include search/filter parameters as needed
- Use 200 status code for success

### GET (Single Resource)
- Use path parameter for resource ID
- Use 200 status code for success
- Include 404 error response

### POST (Create Resource)
- Include requestBody with Create schema
- Use 201 status code for success
- Include validation error responses (400)

### PUT (Update Resource)
- Include path parameter for resource ID
- Include requestBody with Update schema
- Use 200 status code for success
- Include 404 error response

### DELETE (Delete Resource)
- Include path parameter for resource ID
- Use 204 status code for success (no content)
- Include 404 error response

## Schema Naming Conventions

- **Resource Schema**: `{ResourceName}` (e.g., `Bot`, `Flow`, `Entity`)
- **Create Request**: `Create{ResourceName}Request`
- **Update Request**: `Update{ResourceName}Request`
- **Paginated Results**: `Paginated{ResourceName}s`

## Required Elements

### 1. Tags
Always include appropriate tags to group related endpoints:
```yaml
tags: [Bots, Flows, Entities, Languages, etc.]
```

### 2. Security
Include Bearer token authentication for protected endpoints:
```yaml
security:
  - BearerAuth: []
```

### 3. Parameters
- **Path parameters**: Always include description and example
- **Query parameters**: Include type, constraints, and defaults
- **Pagination**: Use standard page/limit parameters

### 4. Request Body
- Reference appropriate schema from swagger.ts
- Include realistic examples
- Mark as required when applicable

### 5. Response Schemas
- Use consistent response structure with `ApiResponse` base
- Reference existing schemas from swagger.ts
- Include all relevant HTTP status codes

### 6. Error Responses
Always include standard error responses:
- `400`: `ValidationErrorResponse`
- `404`: `NotFoundErrorResponse`
- `500`: `InternalServerErrorResponse`

## Examples

### Simple GET Endpoint
```yaml
/**
 * @swagger
 * /api/v1/bots/{id}:
 *   get:
 *     summary: Get bot by ID
 *     description: Retrieves a specific bot by its unique identifier
 *     tags: [Bots]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Bot unique identifier
 *     responses:
 *       200:
 *         description: Bot retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Bot'
 *       404:
 *         description: Bot not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NotFoundErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/InternalServerErrorResponse'
 */
```

### POST Endpoint with Request Body
```yaml
/**
 * @swagger
 * /api/v1/bots:
 *   post:
 *     summary: Create a new bot
 *     description: Creates a new chatbot with the specified configuration
 *     tags: [Bots]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBotRequest'
 *     responses:
 *       201:
 *         description: Bot created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/Bot'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/InternalServerErrorResponse'
 */
```

## Best Practices

1. **Consistency**: Use the same patterns across all endpoints
2. **Completeness**: Include all relevant parameters, responses, and error cases
3. **Examples**: Provide realistic examples for request bodies and parameters
4. **Descriptions**: Write clear, concise descriptions for all elements
5. **Schema References**: Always reference existing schemas from swagger.ts
6. **Error Handling**: Include comprehensive error response documentation
7. **Security**: Always include security requirements for protected endpoints
8. **Validation**: Include proper validation rules and constraints

## Checklist for New Endpoints

- [ ] Endpoint has @swagger comment block
- [ ] Summary and description are clear and informative
- [ ] Appropriate tag is assigned
- [ ] Security requirements are specified
- [ ] All parameters have descriptions and examples
- [ ] Request body schema is referenced correctly
- [ ] All success response codes are documented
- [ ] All error response codes are documented
- [ ] Schema references point to existing schemas in swagger.ts
- [ ] Examples are realistic and helpful
- [ ] YAML syntax is valid (proper indentation with *)

# 🤖 AI-Powered Swagger Documentation Automation Guide

## 📋 Overview

This guide provides a comprehensive framework for AI models to automatically generate, update, and maintain Swagger/OpenAPI documentation for the Bot Builder Service. Use this as context when updating API documentation.

## 🎯 Current State Analysis

### ✅ Documented Endpoints (18 total)

- **Apps**: 4/4 endpoints documented
- **Bots**: 8/12 endpoints documented
- **Flows**: 4/5 endpoints documented
- **Languages**: 2/5 endpoints documented

### ❌ Missing Documentation (47+ endpoints)

- **FAQ Categories**: 0/5 endpoints documented
- **FAQ Items**: 0/5 endpoints documented
- **FAQ Translations**: 0/5 endpoints documented
- **Intent Items**: 0/5 endpoints documented
- **Intent Utterances**: 0/5 endpoints documented
- **Intent Utterance Translations**: 0/5 endpoints documented
- **Entities**: 0/5 endpoints documented
- **Bot Languages**: 0/5 endpoints documented
- **Channel Integrations**: 0/3 endpoints documented

## 🔧 AI Automation Instructions

### Step 1: Analyze Missing Endpoints

```bash
# Scan all controller files for undocumented methods
find src/controllers -name "*.ts" -exec grep -l "public.*async.*Request.*Response" {} \;

# Check route definitions
find src/routers -name "*.ts" -exec grep -E "router\.(get|post|put|delete)" {} \;
```

### Step 2: Generate Documentation Template

For each missing endpoint, use this template:

```typescript
/**
 * @swagger
 * /api/v1/{resource}:
 *   {method}:
 *     summary: {Action} {Resource}
 *     description: {Detailed description of what this endpoint does}
 *     tags: [{ResourceTag}]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: {Resource} unique identifier
 *         example: "123e4567-e89b-12d3-a456-************"
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter results
 *       - in: query
 *         name: include
 *         schema:
 *           type: string
 *         description: Comma-separated list of associations to include
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/{CreateUpdateSchema}'
 *           example:
 *             {exampleFromZodSchema}
 *     responses:
 *       200:
 *         description: {Resource} retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/{ResourceSchema}'
 *       201:
 *         description: {Resource} created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/{ResourceSchema}'
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ValidationErrorResponse'
 *       404:
 *         description: {Resource} not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/NotFoundErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/InternalServerErrorResponse'
 */
```

### Step 3: Resource Mapping

| Controller                           | Resource                      | Tag                           | Schema Prefix              | Routes      |
| ------------------------------------ | ----------------------------- | ----------------------------- | -------------------------- | ----------- |
| FaqCategoryController                | faq-categories                | FAQ Categories                | FaqCategory                | CRUD + list |
| FaqItemsController                   | faq-items                     | FAQ Items                     | FaqItem                    | CRUD + list |
| FaqTranslationController             | faq-translations              | FAQ Translations              | FaqTranslation             | CRUD + list |
| IntentItemsController                | intent-items                  | Intent Items                  | IntentItem                 | CRUD + list |
| IntentUtteranceController            | intent-utterances             | Intent Utterances             | IntentUtterance            | CRUD + list |
| IntentUtteranceTranslationController | intent-utterance-translations | Intent Utterance Translations | IntentUtteranceTranslation | CRUD + list |
| EntitiesController                   | entities                      | Entities                      | Entity                     | CRUD + list |
| LanguageController                   | languages                     | Languages                     | Language                   | CRUD + list |
| BotLanguageController                | bot-languages                 | Bot Languages                 | BotLanguage                | CRUD + list |

### Step 4: Schema Generation

For each resource, ensure these schemas exist in `src/config/swagger.ts`:

```typescript
// Entity Schemas
{ResourceName}: {
  type: "object",
  properties: {
    id: {
      type: "string",
      format: "uuid",
      description: "Unique {resource} identifier"
    },
    // ... other properties based on model
    createdAt: {
      type: "string",
      format: "date-time",
      description: "Creation timestamp"
    },
    updatedAt: {
      type: "string",
      format: "date-time",
      description: "Last update timestamp"
    }
  }
},

Create{ResourceName}Request: {
  type: "object",
  properties: {
    // ... properties from Zod schema
  },
  required: [/* required fields */]
},

Update{ResourceName}Request: {
  type: "object",
  properties: {
    // ... properties from Zod schema (all optional)
  }
},

Paginated{ResourceName}s: {
  type: "object",
  properties: {
    items: {
      type: "array",
      items: {
        $ref: "#/components/schemas/{ResourceName}"
      }
    },
    pagination: {
      $ref: "#/components/schemas/Pagination"
    }
  }
}
```

## 🚀 Execution Workflow

### Phase 1: Documentation Audit

1. Scan all controller files for methods without `@swagger` comments
2. Cross-reference with route definitions to identify HTTP methods and paths
3. Generate list of missing endpoints with their expected schemas

### Phase 2: Schema Generation

1. Add missing schemas to `src/config/swagger.ts`
2. Ensure all CRUD operations have corresponding request/response schemas
3. Add pagination schemas for list endpoints

### Phase 3: Documentation Generation

1. Add comprehensive `@swagger` comments to each undocumented method
2. Use consistent patterns for similar operations (CRUD)
3. Include proper examples, error responses, and parameter documentation

### Phase 4: Validation

1. Start the server and verify Swagger UI loads without errors
2. Test that all endpoints appear in the documentation
3. Validate that "Try it out" functionality works for each endpoint

## 📝 Quality Checklist

- [ ] All controller methods have `@swagger` documentation
- [ ] All schemas referenced in documentation exist in swagger.ts
- [ ] All endpoints appear in Swagger UI
- [ ] Examples are realistic and match schema definitions
- [ ] Error responses are consistently documented
- [ ] Pagination parameters are included for list endpoints
- [ ] Security schemes are properly applied
- [ ] Tags are logically organized
- [ ] No YAML syntax errors in generated spec

## 🔍 Common Issues & Solutions

### Issue: Missing Schema References

**Problem**: `$ref: '#/components/schemas/NonExistentSchema'`
**Solution**: Add the schema definition to `src/config/swagger.ts`

### Issue: YAML Syntax Errors

**Problem**: Malformed YAML in `@swagger` comments
**Solution**: Validate YAML structure, ensure proper indentation

### Issue: Inconsistent Response Patterns

**Problem**: Different response structures across endpoints
**Solution**: Use standardized `ApiResponse` wrapper for all success responses

### Issue: Missing Examples

**Problem**: Empty or unrealistic examples in documentation
**Solution**: Extract examples from Zod schemas using `.openapi()` method

## 🎯 Success Metrics

- **Coverage**: 100% of controller methods documented
- **Functionality**: All endpoints testable via Swagger UI
- **Consistency**: Standardized patterns across all resources
- **Maintainability**: Documentation auto-updates with code changes
- **Developer Experience**: Clear, comprehensive API reference

Use this guide as your primary reference when updating Swagger documentation. Follow the patterns and templates provided to ensure consistency and completeness.

## 🔧 Specific Missing Endpoints Analysis

### FAQ Categories Controller (`/api/v1/faq-categories`)

```typescript
// Missing endpoints:
POST   /api/v1/faq-categories          - Create FAQ category
GET    /api/v1/faq-categories          - List FAQ categories (paginated)
GET    /api/v1/faq-categories/{id}     - Get FAQ category by ID
PUT    /api/v1/faq-categories/{id}     - Update FAQ category
DELETE /api/v1/faq-categories/{id}     - Delete FAQ category
```

### FAQ Items Controller (`/api/v1/faq-items`)

```typescript
// Missing endpoints:
POST   /api/v1/faq-items               - Create FAQ item
GET    /api/v1/faq-items               - List FAQ items (paginated)
GET    /api/v1/faq-items/{id}          - Get FAQ item by ID
PUT    /api/v1/faq-items/{id}          - Update FAQ item
DELETE /api/v1/faq-items/{id}          - Delete FAQ item
```

### FAQ Translations Controller (`/api/v1/faq-translations`)

```typescript
// Missing endpoints:
POST   /api/v1/faq-translations        - Create FAQ translation
GET    /api/v1/faq-translations        - List FAQ translations (paginated)
GET    /api/v1/faq-translations/{id}   - Get FAQ translation by ID
PUT    /api/v1/faq-translations/{id}   - Update FAQ translation
DELETE /api/v1/faq-translations/{id}   - Delete FAQ translation
```

### Intent Items Controller (`/api/v1/intent-items`)

```typescript
// Missing endpoints:
POST   /api/v1/intent-items            - Create intent item
GET    /api/v1/intent-items            - List intent items (paginated)
GET    /api/v1/intent-items/{id}       - Get intent item by ID
PUT    /api/v1/intent-items/{id}       - Update intent item
DELETE /api/v1/intent-items/{id}       - Delete intent item
```

### Intent Utterances Controller (`/api/v1/intent-utterances`)

```typescript
// Missing endpoints:
POST   /api/v1/intent-utterances       - Create intent utterance
GET    /api/v1/intent-utterances       - List intent utterances (paginated)
GET    /api/v1/intent-utterances/{id}  - Get intent utterance by ID
PUT    /api/v1/intent-utterances/{id}  - Update intent utterance
DELETE /api/v1/intent-utterances/{id}  - Delete intent utterance
```

### Intent Utterance Translations Controller (`/api/v1/intent-utterance-translations`)

```typescript
// Missing endpoints:
POST   /api/v1/intent-utterance-translations       - Create intent utterance translation
GET    /api/v1/intent-utterance-translations       - List intent utterance translations (paginated)
GET    /api/v1/intent-utterance-translations/{id}  - Get intent utterance translation by ID
PUT    /api/v1/intent-utterance-translations/{id}  - Update intent utterance translation
DELETE /api/v1/intent-utterance-translations/{id}  - Delete intent utterance translation
```

### Entities Controller (`/api/v1/entities`)

```typescript
// Missing endpoints:
POST   /api/v1/entities                - Create entity
GET    /api/v1/entities                - List entities (paginated)
GET    /api/v1/entities/{id}           - Get entity by ID
PUT    /api/v1/entities/{id}           - Update entity
DELETE /api/v1/entities/{id}           - Delete entity
```

### Languages Controller (`/api/v1/languages`)

```typescript
// Missing endpoints:
GET    /api/v1/languages/{id}          - Get language by ID
PUT    /api/v1/languages/{id}          - Update language
DELETE /api/v1/languages/{id}          - Delete language
```

### Bot Languages Controller (`/api/v1/bot-languages`)

```typescript
// Missing endpoints:
POST   /api/v1/bot-languages           - Create bot language
GET    /api/v1/bot-languages           - List bot languages (paginated)
GET    /api/v1/bot-languages/{id}      - Get bot language by ID
PUT    /api/v1/bot-languages/{id}      - Update bot language
DELETE /api/v1/bot-languages/{id}      - Delete bot language
```

### Bots Controller (`/api/v1/bots`)

```typescript
// Missing endpoints:
GET    /api/v1/bots                    - List bots (paginated) - NEEDS DOCUMENTATION
POST   /api/v1/bots/{id}/activate      - Activate bot - NEEDS DOCUMENTATION
POST   /api/v1/bots/{id}/deactivate    - Deactivate bot - NEEDS DOCUMENTATION
POST   /api/v1/bots/{id}/build         - Build bot - NEEDS DOCUMENTATION
POST   /api/v1/bots/{id}/clone         - Clone bot - NEEDS DOCUMENTATION
```

## 🎨 Schema Templates by Resource Type

### FAQ Category Schema Template

```typescript
FaqCategory: {
  type: "object",
  properties: {
    id: { type: "string", format: "uuid" },
    name: { type: "string", description: "Category name" },
    description: { type: "string", description: "Category description" },
    botId: { type: "string", format: "uuid", description: "Associated bot ID" },
    isActive: { type: "boolean", description: "Whether category is active" },
    createdAt: { type: "string", format: "date-time" },
    updatedAt: { type: "string", format: "date-time" },
    createdBy: { type: "string", format: "uuid" },
    updatedBy: { type: "string", format: "uuid" }
  }
}
```

### FAQ Item Schema Template

```typescript
FaqItem: {
  type: "object",
  properties: {
    id: { type: "string", format: "uuid" },
    botId: { type: "string", format: "uuid" },
    categoryId: { type: "string", format: "uuid" },
    flowId: { type: "string", format: "uuid", nullable: true },
    question: { type: "string", description: "FAQ question" },
    answer: { type: "string", description: "FAQ answer" },
    isActive: { type: "boolean" },
    createdAt: { type: "string", format: "date-time" },
    updatedAt: { type: "string", format: "date-time" }
  }
}
```

### Intent Item Schema Template

```typescript
IntentItem: {
  type: "object",
  properties: {
    id: { type: "string", format: "uuid" },
    name: { type: "string", description: "Intent name" },
    botId: { type: "string", format: "uuid" },
    flowId: { type: "string", format: "uuid", nullable: true },
    isActive: { type: "boolean" },
    metadata: { type: "object", nullable: true },
    createdAt: { type: "string", format: "date-time" },
    updatedAt: { type: "string", format: "date-time" }
  }
}
```

## 🚀 Quick Start Commands

### 1. Analyze Current State

```bash
# Count documented vs undocumented endpoints
grep -r "@swagger" src/controllers/ | wc -l
grep -r "public.*async.*Request.*Response" src/controllers/ | wc -l
```

### 2. Generate Missing Schemas

```bash
# Extract Zod schemas for reference
grep -r "export.*Schema.*z\." src/schemas/
```

### 3. Validate Documentation

```bash
# Start server and check Swagger UI
npm run dev
# Open http://localhost:3000/api-docs
```

### 4. Test Completeness

```bash
# Verify all routes are documented
curl http://localhost:3000/api-docs/swagger.json | jq '.paths | keys[]'
```

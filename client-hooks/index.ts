// client-hooks/index.ts

import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

// Common API types
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  filter?: Record<string, any>;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

interface UuidParams {
  id: string;
}

interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Define API types based on your backend schemas
// FAQ Category types
interface FaqCategory {
  id: string;
  botId: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

interface CreateFaqCategoryRequest {
  botId: string;
  name: string;
  description?: string;
}

interface UpdateFaqCategoryRequest {
  botId?: string;
  name?: string;
  description?: string;
}

// FAQ Item types
interface FaqItem {
  id: string;
  botId: string;
  flowId?: string;
  categoryId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
  category?: FaqCategory;
}

interface CreateFaqItemRequest {
  botId: string;
  flowId?: string;
  categoryId: string;
}

interface UpdateFaqItemRequest {
  botId?: string;
  flowId?: string;
  categoryId?: string;
}

// FAQ Translation types
interface FaqTranslation {
  id: string;
  faqId: string;
  langId: string;
  questions: string[];
  answer: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

interface CreateFaqTranslationRequest {
  faqId: string;
  langId: string;
  questions: string[];
  answer: string;
  metadata?: Record<string, any>;
}

interface UpdateFaqTranslationRequest {
  questions?: string[];
  answer?: string;
  metadata?: Record<string, any>;
}

// Intent Item types
interface IntentItem {
  id: string;
  botId: string;
  flowId?: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

interface CreateIntentItemRequest {
  botId: string;
  flowId?: string;
  name: string;
  description?: string;
}

interface UpdateIntentItemRequest {
  botId?: string;
  flowId?: string;
  name?: string;
  description?: string;
}

// Intent Utterance types
interface IntentUtterance {
  id: string;
  intentId: string;
  metadata?: Record<string, any>;
  createdAt: string;
  createdBy: string;
}

interface CreateIntentUtteranceRequest {
  intentId: string;
  metadata?: Record<string, any>;
}

interface UpdateIntentUtteranceRequest {
  metadata?: Record<string, any>;
}

// Intent Utterance Translation types
interface IntentUtteranceTranslation {
  id: string;
  utteranceId: string;
  langId: string;
  text: string;
  entities?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

interface CreateIntentUtteranceTranslationRequest {
  utteranceId: string;
  langId: string;
  text: string;
  entities?: Record<string, any>;
}

interface UpdateIntentUtteranceTranslationRequest {
  text?: string;
  entities?: Record<string, any>;
}

// Entity types
interface Entity {
  id: string;
  name: string;
  botId: string;
  intentId: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;
}

interface CreateEntityRequest {
  botId: string;
  intentId: string;
  name: string;
  metadata?: Record<string, any>;
}

interface UpdateEntityRequest {
  botId?: string;
  intentId?: string;
  name?: string;
  metadata?: Record<string, any>;
}

// Language types
interface Language {
  id: string;
  name: string;
  code: string;
  createdAt: string;
}

interface CreateLanguageRequest {
  name: string;
  code: string;
}

interface UpdateLanguageRequest {
  name?: string;
  code?: string;
}

// Bot Language types
interface BotLanguage {
  id: string;
  botId: string;
  langId: string;
  createdAt: string;
  createdBy: string;
}

interface CreateBotLanguageRequest {
  botId: string;
  langId: string;
}

interface UpdateBotLanguageRequest {
  botId?: string;
  langId?: string;
}

// Define a base API slice (assuming it's not defined elsewhere)
const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({ baseUrl: "/api/v1" }), // Adjust base URL as needed
  endpoints: (builder) => ({}),
});

const updatedApiSlice = apiSlice.enhanceEndpoints({
  addTagTypes: [
    "FaqCategory", 
    "FaqItem", 
    "FaqTranslation", 
    "IntentItem", 
    "IntentUtterance", 
    "IntentUtteranceTranslation", 
    "Entity", 
    "Language", 
    "BotLanguage"
  ],
});

export const knowledgeApi = updatedApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // FAQ Categories
    getFaqCategories: builder.query<ApiResponse<PaginatedResponse<FaqCategory>>, PaginationParams>({
      query: (params) => ({
        url: "/faq-categories",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["FaqCategory"],
    }),
    getFaqCategory: builder.query<ApiResponse<FaqCategory>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-categories/${id}` }),
      providesTags: ["FaqCategory"],
    }),
    createFaqCategory: builder.mutation<ApiResponse<FaqCategory>, CreateFaqCategoryRequest>({
      query: (body) => ({
        url: "/faq-categories",
        method: "POST",
        body,
      }),
      invalidatesTags: ["FaqCategory"],
    }),
    updateFaqCategory: builder.mutation<ApiResponse<FaqCategory>, UuidParams & UpdateFaqCategoryRequest>({
      query: ({ id, ...body }) => ({
        url: `/faq-categories/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["FaqCategory"],
    }),
    deleteFaqCategory: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-categories/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["FaqCategory"],
    }),

    // FAQ Items
    getFaqItems: builder.query<ApiResponse<PaginatedResponse<FaqItem>>, PaginationParams>({
      query: (params) => ({
        url: "/faq-items",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["FaqItem"],
    }),
    getFaqItem: builder.query<ApiResponse<FaqItem>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-items/${id}` }),
      providesTags: ["FaqItem"],
    }),
    createFaqItem: builder.mutation<ApiResponse<FaqItem>, CreateFaqItemRequest>({
      query: (body) => ({
        url: "/faq-items",
        method: "POST",
        body,
      }),
      invalidatesTags: ["FaqItem"],
    }),
    updateFaqItem: builder.mutation<ApiResponse<FaqItem>, UuidParams & UpdateFaqItemRequest>({
      query: ({ id, ...body }) => ({
        url: `/faq-items/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["FaqItem"],
    }),
    deleteFaqItem: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["FaqItem"],
    }),

    // FAQ Translations
    getFaqTranslations: builder.query<ApiResponse<PaginatedResponse<FaqTranslation>>, PaginationParams>({
      query: (params) => ({
        url: "/faq-translations",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["FaqTranslation"],
    }),
    getFaqTranslation: builder.query<ApiResponse<FaqTranslation>, UuidParams>({
      query: ({ id }) => ({ url: `/faq-translations/${id}` }),
      providesTags: ["FaqTranslation"],
    }),
    createFaqTranslation: builder.mutation<ApiResponse<FaqTranslation>, CreateFaqTranslationRequest>({
      query: (body) => ({
        url: "/faq-translations",
        method: "POST",
        body,
      }),
      invalidatesTags: ["FaqTranslation"],
    }),
    updateFaqTranslation: builder.mutation<ApiResponse<FaqTranslation>, UuidParams & UpdateFaqTranslationRequest>({
      query: ({ id, ...body }) => ({
        url: `/faq-translations/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["FaqTranslation"],
    }),
    deleteFaqTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/faq-translations/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["FaqTranslation"],
    }),

    // Intent Items
    getIntentItems: builder.query<ApiResponse<PaginatedResponse<IntentItem>>, PaginationParams>({
      query: (params) => ({
        url: "/intent-items",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["IntentItem"],
    }),
    getIntentItem: builder.query<ApiResponse<IntentItem>, UuidParams>({
      query: ({ id }) => ({ url: `/intent-items/${id}` }),
      providesTags: ["IntentItem"],
    }),
    createIntentItem: builder.mutation<ApiResponse<IntentItem>, CreateIntentItemRequest>({
      query: (body) => ({
        url: "/intent-items",
        method: "POST",
        body,
      }),
      invalidatesTags: ["IntentItem"],
    }),
    updateIntentItem: builder.mutation<ApiResponse<IntentItem>, UuidParams & UpdateIntentItemRequest>({
      query: ({ id, ...body }) => ({
        url: `/intent-items/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["IntentItem"],
    }),
    deleteIntentItem: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-items/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["IntentItem"],
    }),

    // Intent Utterances
    getIntentUtterances: builder.query<ApiResponse<PaginatedResponse<IntentUtterance>>, PaginationParams>({
      query: (params) => ({
        url: "/intent-utterances",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["IntentUtterance"],
    }),
    getIntentUtterance: builder.query<ApiResponse<IntentUtterance>, UuidParams>({
      query: ({ id }) => ({ url: `/intent-utterances/${id}` }),
      providesTags: ["IntentUtterance"],
    }),
    createIntentUtterance: builder.mutation<ApiResponse<IntentUtterance>, CreateIntentUtteranceRequest>({
      query: (body) => ({
        url: "/intent-utterances",
        method: "POST",
        body,
      }),
      invalidatesTags: ["IntentUtterance"],
    }),
    updateIntentUtterance: builder.mutation<ApiResponse<IntentUtterance>, UuidParams & UpdateIntentUtteranceRequest>({
      query: ({ id, ...body }) => ({
        url: `/intent-utterances/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["IntentUtterance"],
    }),
    deleteIntentUtterance: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-utterances/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["IntentUtterance"],
    }),

    // Intent Utterance Translations
    getIntentUtteranceTranslations: builder.query<ApiResponse<PaginatedResponse<IntentUtteranceTranslation>>, PaginationParams>({
      query: (params) => ({
        url: "/intent-utterance-translations",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["IntentUtteranceTranslation"],
    }),
    getIntentUtteranceTranslation: builder.query<ApiResponse<IntentUtteranceTranslation>, UuidParams>({
      query: ({ id }) => ({ url: `/intent-utterance-translations/${id}` }),
      providesTags: ["IntentUtteranceTranslation"],
    }),
    createIntentUtteranceTranslation: builder.mutation<ApiResponse<IntentUtteranceTranslation>, CreateIntentUtteranceTranslationRequest>({
      query: (body) => ({
        url: "/intent-utterance-translations",
        method: "POST",
        body,
      }),
      invalidatesTags: ["IntentUtteranceTranslation"],
    }),
    updateIntentUtteranceTranslation: builder.mutation<ApiResponse<IntentUtteranceTranslation>, UuidParams & UpdateIntentUtteranceTranslationRequest>({
      query: ({ id, ...body }) => ({
        url: `/intent-utterance-translations/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["IntentUtteranceTranslation"],
    }),
    deleteIntentUtteranceTranslation: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/intent-utterance-translations/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["IntentUtteranceTranslation"],
    }),

    // Entities
    getEntities: builder.query<ApiResponse<PaginatedResponse<Entity>>, PaginationParams>({
      query: (params) => ({
        url: "/entities",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["Entity"],
    }),
    getEntity: builder.query<ApiResponse<Entity>, UuidParams>({
      query: ({ id }) => ({ url: `/entities/${id}` }),
      providesTags: ["Entity"],
    }),
    createEntity: builder.mutation<ApiResponse<Entity>, CreateEntityRequest>({
      query: (body) => ({
        url: "/entities",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Entity"],
    }),
    updateEntity: builder.mutation<ApiResponse<Entity>, UuidParams & UpdateEntityRequest>({
      query: ({ id, ...body }) => ({
        url: `/entities/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Entity"],
    }),
    deleteEntity: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/entities/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Entity"],
    }),

    // Languages
    getLanguages: builder.query<ApiResponse<PaginatedResponse<Language>>, PaginationParams>({
      query: (params) => ({
        url: "/languages",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["Language"],
    }),
    getLanguage: builder.query<ApiResponse<Language>, UuidParams>({
      query: ({ id }) => ({ url: `/languages/${id}` }),
      providesTags: ["Language"],
    }),
    createLanguage: builder.mutation<ApiResponse<Language>, CreateLanguageRequest>({
      query: (body) => ({
        url: "/languages",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Language"],
    }),
    updateLanguage: builder.mutation<ApiResponse<Language>, UuidParams & UpdateLanguageRequest>({
      query: ({ id, ...body }) => ({
        url: `/languages/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Language"],
    }),
    deleteLanguage: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/languages/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Language"],
    }),

    // Bot Languages
    getBotLanguages: builder.query<ApiResponse<PaginatedResponse<BotLanguage>>, PaginationParams>({
      query: (params) => ({
        url: "/bot-languages",
        params: {
          ...params,
          ...(params.filter && { filter: JSON.stringify(params.filter) }),
        },
      }),
      providesTags: ["BotLanguage"],
    }),
    getBotLanguage: builder.query<ApiResponse<BotLanguage>, UuidParams>({
      query: ({ id }) => ({ url: `/bot-languages/${id}` }),
      providesTags: ["BotLanguage"],
    }),
    createBotLanguage: builder.mutation<ApiResponse<BotLanguage>, CreateBotLanguageRequest>({
      query: (body) => ({
        url: "/bot-languages",
        method: "POST",
        body,
      }),
      invalidatesTags: ["BotLanguage"],
    }),
    updateBotLanguage: builder.mutation<ApiResponse<BotLanguage>, UuidParams & UpdateBotLanguageRequest>({
      query: ({ id, ...body }) => ({
        url: `/bot-languages/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["BotLanguage"],
    }),
    deleteBotLanguage: builder.mutation<void, UuidParams>({
      query: ({ id }) => ({
        url: `/bot-languages/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["BotLanguage"],
    }),
  }),
});

export const {
  // FAQ Categories
  useGetFaqCategoriesQuery,
  useGetFaqCategoryQuery,
  useCreateFaqCategoryMutation,
  useUpdateFaqCategoryMutation,
  useDeleteFaqCategoryMutation,
  
  // FAQ Items
  useGetFaqItemsQuery,
  useGetFaqItemQuery,
  useCreateFaqItemMutation,
  useUpdateFaqItemMutation,
  useDeleteFaqItemMutation,
  
  // FAQ Translations
  useGetFaqTranslationsQuery,
  useGetFaqTranslationQuery,
  useCreateFaqTranslationMutation,
  useUpdateFaqTranslationMutation,
  useDeleteFaqTranslationMutation,
  
  // Intent Items
  useGetIntentItemsQuery,
  useGetIntentItemQuery,
  useCreateIntentItemMutation,
  useUpdateIntentItemMutation,
  useDeleteIntentItemMutation,
  
  // Intent Utterances
  useGetIntentUtterancesQuery,
  useGetIntentUtteranceQuery,
  useCreateIntentUtteranceMutation,
  useUpdateIntentUtteranceMutation,
  useDeleteIntentUtteranceMutation,
  
  // Intent Utterance Translations
  useGetIntentUtteranceTranslationsQuery,
  useGetIntentUtteranceTranslationQuery,
  useCreateIntentUtteranceTranslationMutation,
  useUpdateIntentUtteranceTranslationMutation,
  useDeleteIntentUtteranceTranslationMutation,
  
  // Entities
  useGetEntitiesQuery,
  useGetEntityQuery,
  useCreateEntityMutation,
  useUpdateEntityMutation,
  useDeleteEntityMutation,
  
  // Languages
  useGetLanguagesQuery,
  useGetLanguageQuery,
  useCreateLanguageMutation,
  useUpdateLanguageMutation,
  useDeleteLanguageMutation,
  
  // Bot Languages
  useGetBotLanguagesQuery,
  useGetBotLanguageQuery,
  useCreateBotLanguageMutation,
  useUpdateBotLanguageMutation,
  useDeleteBotLanguageMutation,
} = knowledgeApi;
